-- ============================================================================
-- MDM V7.0 Dashboard 完整测试数据生成脚本 (整合版) - MySQL版本
-- 整合了所有SQL文件的功能，确保数据完整性和一致性
-- 目标ORG_ID: 89344c8b02004e5692baa58a7bfabab7
-- 生成时间: 2025-01-11
-- 整合文件: 
--   1. generate_dashboard_test_data_fixed.sql (主体)
--   2. fix_missing_dashboard_data.sql (VEE和Summary修复)
--   3. fix_summary_object_complete.sql (Summary Object表结构)
--   4. fix_vee_and_summary_final.sql (VEE和Summary最终修复)
-- ============================================================================

-- ============================================================================
-- 第一部分：清理现有数据
-- ============================================================================

-- 正在清理现有测试数据...

-- 清理现有测试数据
DELETE FROM MDM_DATA_STATISTICS WHERE ORG_ID = '89344c8b02004e5692baa58a7bfabab7';
DELETE FROM MDM_DATA_OUTAGE_STATISTICS WHERE ORG_ID = '89344c8b02004e5692baa58a7bfabab7';

-- 清理Summary Object相关表数据（根据实际表结构调整）
-- 检查表是否存在并清理数据
SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'MDM_DATA_CALC_OBJ_ENERGY') > 0,
    'DELETE FROM MDM_DATA_CALC_OBJ_ENERGY WHERE SCHEME_ID = ''7''',
    'SELECT ''MDM_DATA_CALC_OBJ_ENERGY表不存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'MDM_ASSET_CALC_OBJ') > 0,
    'DELETE FROM MDM_ASSET_CALC_OBJ WHERE ORG_ID = ''89344c8b02004e5692baa58a7bfabab7''',
    'SELECT ''MDM_ASSET_CALC_OBJ表不存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'MDM_ASSET_CALC_SCHEME') > 0,
    'DELETE FROM MDM_ASSET_CALC_SCHEME WHERE ID = ''7''',
    'SELECT ''MDM_ASSET_CALC_SCHEME表不存在'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ============================================================================
-- 第二部分：创建Summary Object Statistics所需的表结构（如果不存在）
-- ============================================================================

-- 正在检查和创建Summary Object Statistics相关表...

-- 检查表是否存在
SELECT 
    CASE WHEN COUNT(*) > 0 THEN 'MDM_ASSET_CALC_SCHEME表已存在' ELSE '警告：MDM_ASSET_CALC_SCHEME表不存在，请手动创建' END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'MDM_ASSET_CALC_SCHEME';

SELECT 
    CASE WHEN COUNT(*) > 0 THEN 'MDM_ASSET_CALC_OBJ表已存在' ELSE '警告：MDM_ASSET_CALC_OBJ表不存在，请手动创建' END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'MDM_ASSET_CALC_OBJ';

SELECT 
    CASE WHEN COUNT(*) > 0 THEN 'MDM_DATA_CALC_OBJ_ENERGY表已存在' ELSE '警告：MDM_DATA_CALC_OBJ_ENERGY表不存在，请手动创建' END as status
FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'MDM_DATA_CALC_OBJ_ENERGY';

-- ============================================================================
-- 第三部分：插入Summary Object Statistics基础配置数据
-- ============================================================================

-- 正在插入Summary Object Statistics基础配置数据...

-- 1. 插入计算方案 (scheme_id=7是查询中的固定条件)
-- 根据实际表结构调整字段
INSERT INTO MDM_ASSET_CALC_SCHEME (ID, NAME, TYPE, IS_USE, CYCLE_NUM)
VALUES ('7', 'Daily Energy Summary Scheme', 2, 1, 1);

-- 2. 插入计算对象 (ENTITY_TYPE=5, entity_id为组织ID)
-- 根据实际表结构调整字段，TYPE字段为NUMBER类型
INSERT INTO MDM_ASSET_CALC_OBJ (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_001', 'Main Transformer A', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

INSERT INTO MDM_ASSET_CALC_OBJ (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_002', 'Main Transformer B', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

INSERT INTO MDM_ASSET_CALC_OBJ (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_003', 'Distribution Network A', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

INSERT INTO MDM_ASSET_CALC_OBJ (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_004', 'Distribution Network B', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

INSERT INTO MDM_ASSET_CALC_OBJ (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_005', 'Feeder Line 1', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

INSERT INTO MDM_ASSET_CALC_OBJ (ID, NAME, ENTITY_TYPE, ENTITY_ID, TYPE, ORG_ID)
VALUES ('CALC_OBJ_006', 'Feeder Line 2', 5, '89344c8b02004e5692baa58a7bfabab7', 2, '89344c8b02004e5692baa58a7bfabab7');

-- 3. 插入能源数据 (scheme_id=7对应上面的方案)
-- 根据实际表结构调整：没有ID和ORG_ID字段，SCHEME_ID为VARCHAR类型
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 1250.5, 980.2, 1180.3, 920.1);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 950.8, 750.6, 890.4, 680.2);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 680.2, 520.8, 640.1, 480.3);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_004', '7', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 420.6, 350.4, 390.2, 310.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_005', '7', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 280.4, 230.6, 260.2, 210.4);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_006', '7', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 195.8, 165.2, 180.6, 150.4);

-- 添加历史数据
INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_001', '7', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 1180.3, 950.5, 1120.8, 890.6);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_002', '7', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 890.2, 720.4, 850.6, 650.8);

INSERT INTO MDM_DATA_CALC_OBJ_ENERGY (CALC_OBJ_ID, SCHEME_ID, TV, R0P1, R0P2, R1P1, R1P2)
VALUES ('CALC_OBJ_003', '7', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 650.8, 490.2, 610.5, 450.7);

-- ============================================================================
-- 第四部分：Asset Dashboard数据
-- ============================================================================

-- 正在生成Asset Dashboard数据...

-- 1. Asset Dashboard - Provisioning Statistics (设备配置统计)
-- ID: 1145010, lineFlag: 5 (电表运行状态字典)
-- 使用字典表5的实际inner_value: 1=To be installed, 3=Running, 9=Remove

-- 昨日设备配置统计
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145010', '1', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 15, 25.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145010', '3', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 35, 58.33);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145010', '9', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 10, 16.67);

-- 历史数据
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145010', '1', DATE_SUB(CURDATE(), INTERVAL 3 DAY), 12, 24.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145010', '3', DATE_SUB(CURDATE(), INTERVAL 3 DAY), 32, 64.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145010', '9', DATE_SUB(CURDATE(), INTERVAL 3 DAY), 6, 12.0);

-- 2. Asset Dashboard - Data Collection Progress Statistics (数据收集进度统计)
-- ID: 1145020, lineFlag: 1107 (数据类型字典)
-- 使用字典表1107的实际inner_value: 102=Interval Data, 103=Instantaneous Data, 106=VEE Event Data, 109=Device Event Data

-- 近7天数据收集进度统计
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '102', CURDATE(), 450, 85.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '102', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 440, 83.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '103', CURDATE(), 380, 72.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '103', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 375, 71.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '106', CURDATE(), 290, 55.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '106', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 285, 54.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '109', CURDATE(), 320, 60.5);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145020', '109', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 315, 59.5);

-- ============================================================================
-- 第五部分：Data Analysis Dashboard数据
-- ============================================================================

-- 正在生成Data Analysis Dashboard数据...

-- 3. Data Analysis Dashboard - Device Events Statistics (设备事件统计)
-- ID: 1145030, lineFlag: 20000 (设备事件字典)
-- 使用字典表20000的实际inner_value: 20001=Standard Event, 20002=Fraud Event, 20003=Relay Control Event, 20005=Communication Event

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145030', '20001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 25, 35.7);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145030', '20002', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 18, 25.7);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145030', '20003', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 12, 17.1);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145030', '20005', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 15, 21.4);

-- 4. Data Analysis Dashboard - VEE Events Statistics (VEE事件统计)  
-- ID: 1145040, lineFlag: 异常类型字典 (动态变化)
-- 为多种异常类型字典ID添加数据，解决exceptionId为undefined的问题

-- 为常见的异常类型字典ID添加数据
-- 字典ID=1106 的VEE事件统计 (默认异常类型)
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1106001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 45, 42.8);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1106002', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 32, 30.5);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1106003', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 28, 26.7);

-- 字典ID=1001 的VEE事件统计 (数据验证异常)
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1001001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 25, 23.8);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1001002', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 18, 17.1);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1001003', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 15, 14.3);

-- 字典ID=1002 的VEE事件统计 (数据估算异常)
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1002001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 22, 20.9);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1002002', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 20, 19.0);

-- 通用数字ID（兼容性）
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 45, 42.8);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '2', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 32, 30.5);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '3', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 28, 26.7);

-- VEE事件历史数据
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '1', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 40, 40.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '2', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 30, 30.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145040', '3', DATE_SUB(CURDATE(), INTERVAL 2 DAY), 25, 25.0);

-- 5. Data Analysis Dashboard - VEE Data Statistics (VEE数据统计)
-- ID: 1145050, lineFlag: 1105 (数据源字典)
-- 使用字典表1105的实际inner_value: 0=Collection, 1=Manual Estimation, 2=Auto Estimation

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145050', '0', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 350, 58.3);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145050', '1', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 150, 25.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145050', '2', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 100, 16.7);

-- ============================================================================
-- 第六部分：Energy Analysis Dashboard数据
-- ============================================================================

-- 正在生成Energy Analysis Dashboard数据...

-- 6. Energy Analysis Dashboard - Loss Object Statistics (损耗对象统计)
-- ID: 1145060, lineFlag: 1010 (损耗类型字典)  

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145060', '1010001', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 20, 16.7);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145060', '1', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 85, 70.8);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145060', '2', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 10, 8.3);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145060', '3', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 5, 4.2);

-- 7. Energy Analysis Dashboard - Billing Statistics (计费统计)
-- ID: 1145070, lineFlag: 1003 (计费类型字典)
-- 使用字典表1003的实际inner_value: 1003001=Missing Data (Billing Data), 1003002=Abnormal smaller consumption

-- 上月计费统计
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1003001', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01'), 70, 12.5);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1003002', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01'), 35, 6.25);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01'), 420, 75.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '2', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01'), 35, 6.25);

-- 历史月度数据
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1003001', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 MONTH), '%Y-%m-01'), 65, 11.8);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1003002', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 MONTH), '%Y-%m-01'), 40, 7.2);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 MONTH), '%Y-%m-01'), 410, 74.5);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '2', DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 MONTH), '%Y-%m-01'), 35, 6.5);

-- 本月数据（修复Billing Statistics表格下半部分没有数据的问题）
INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1003001', DATE_FORMAT(CURDATE(), '%Y-%m-01'), 45, 9.5);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1003002', DATE_FORMAT(CURDATE(), '%Y-%m-01'), 28, 5.9);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '1', DATE_FORMAT(CURDATE(), '%Y-%m-01'), 380, 80.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145070', '2', DATE_FORMAT(CURDATE(), '%Y-%m-01'), 22, 4.6);

-- 8. Energy Analysis Dashboard - Summary Object Statistics (汇总对象统计)
-- ID: 1145080, 在MDM_DATA_STATISTICS表中创建汇总数据作为备用方案

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145080', 'TRANSFORMER_A', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 1250000, 100.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145080', 'TRANSFORMER_B', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 950000, 76.0);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145080', 'FEEDER_1', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 680000, 54.4);

INSERT INTO MDM_DATA_STATISTICS (ORG_ID, ID, ID_TYPE, TV, COUNT_CURRENT, PERCENT) 
VALUES ('89344c8b02004e5692baa58a7bfabab7', '1145080', 'FEEDER_2', DATE_SUB(CURDATE(), INTERVAL 1 DAY), 520000, 41.6);

-- ============================================================================
-- 第七部分：停电统计数据
-- ============================================================================

-- 正在生成停电统计数据...

-- 停电统计数据 (MDM_DATA_OUTAGE_STATISTICS)
INSERT INTO MDM_DATA_OUTAGE_STATISTICS (
    ORG_ID, LINE_ID, SDP_TYPE, TIME_TYPE, TV, 
    SDP_TOTAL_NUM, TOTAL_TIME, TOTAL_NUM, AVG_TIME, AVG_NUM
) VALUES (
    '89344c8b02004e5692baa58a7bfabab7', 'LINE_001', 0, 2, DATE_SUB(CURDATE(), INTERVAL 1 DAY),
    500, 1200, 25, 2.4, 0.05
);

INSERT INTO MDM_DATA_OUTAGE_STATISTICS (
    ORG_ID, LINE_ID, SDP_TYPE, TIME_TYPE, TV, 
    SDP_TOTAL_NUM, TOTAL_TIME, TOTAL_NUM, AVG_TIME, AVG_NUM
) VALUES (
    '89344c8b02004e5692baa58a7bfabab7', 'LINE_002', 0, 2, DATE_SUB(CURDATE(), INTERVAL 1 DAY),
    350, 840, 18, 2.4, 0.051
);

-- 本月停电统计
INSERT INTO MDM_DATA_OUTAGE_STATISTICS (
    ORG_ID, LINE_ID, SDP_TYPE, TIME_TYPE, TV, 
    SDP_TOTAL_NUM, TOTAL_TIME, TOTAL_NUM, AVG_TIME, AVG_NUM
) VALUES (
    '89344c8b02004e5692baa58a7bfabab7', 'LINE_001', 0, 3, DATE_FORMAT(CURDATE(), '%Y-%m-01'),
    500, 3600, 75, 7.2, 0.15
);

INSERT INTO MDM_DATA_OUTAGE_STATISTICS (
    ORG_ID, LINE_ID, SDP_TYPE, TIME_TYPE, TV, 
    SDP_TOTAL_NUM, TOTAL_TIME, TOTAL_NUM, AVG_TIME, AVG_NUM
) VALUES (
    '89344c8b02004e5692baa58a7bfabab7', 'LINE_002', 0, 3, DATE_FORMAT(CURDATE(), '%Y-%m-01'),
    350, 2520, 52, 7.2, 0.149
);

-- ============================================================================
-- 第八部分：提交并验证数据
-- ============================================================================

COMMIT;

-- 正在验证生成的数据...

-- 验证MDM_DATA_STATISTICS数据
SELECT 
    ID,
    COUNT(DISTINCT ID_TYPE) as TYPE_COUNT,
    COUNT(*) as RECORD_COUNT,
    MIN(TV) as MIN_DATE,
    MAX(TV) as MAX_DATE,
    SUM(COUNT_CURRENT) as TOTAL_COUNT
FROM MDM_DATA_STATISTICS 
WHERE ORG_ID = '89344c8b02004e5692baa58a7bfabab7'
GROUP BY ID
ORDER BY ID;

-- 验证Summary Object Statistics数据
SELECT 
    CASE WHEN COUNT(*) > 0 THEN CONCAT('Summary Object Energy Data Count: ', COUNT(*))
         ELSE 'MDM_DATA_CALC_OBJ_ENERGY表不存在或无数据' END as message
FROM information_schema.tables t1
LEFT JOIN MDM_DATA_CALC_OBJ_ENERGY t2 ON t2.SCHEME_ID = '7'
WHERE t1.table_schema = DATABASE() AND t1.table_name = 'MDM_DATA_CALC_OBJ_ENERGY';

-- 测试Summary Object查询（如果表存在）
SELECT '测试Summary Object查询:' as message;

SELECT t.R0P1, t1.NAME as calcObjName, t.TV
FROM MDM_DATA_CALC_OBJ_ENERGY t
INNER JOIN MDM_ASSET_CALC_OBJ t1 ON t.calc_obj_id = t1.id AND t1.ENTITY_TYPE = 5
LEFT JOIN MDM_ASSET_CALC_SCHEME t2 ON t.scheme_id = t2.id AND t2.is_use = 1 AND t2.cycle_num = 1 AND t2.type = 2
WHERE t.scheme_id = '7'
AND DATE_FORMAT(t.TV,'%Y-%m-%d') >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY),'%Y-%m-%d')
AND DATE_FORMAT(t.TV,'%Y-%m-%d') <= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY),'%Y-%m-%d')
AND t1.entity_id IN ('89344c8b02004e5692baa58a7bfabab7')
ORDER BY t.R0P1 DESC;

-- 验证停电统计数据
SELECT 
    'MDM_DATA_OUTAGE_STATISTICS' as TABLE_NAME,
    COUNT(*) as TOTAL_RECORDS
FROM MDM_DATA_OUTAGE_STATISTICS 
WHERE ORG_ID = '89344c8b02004e5692baa58a7bfabab7';

-- 总记录数统计
SELECT 
    'MDM_DATA_STATISTICS' as TABLE_NAME,
    COUNT(*) as TOTAL_RECORDS
FROM MDM_DATA_STATISTICS 
WHERE ORG_ID = '89344c8b02004e5692baa58a7bfabab7'
UNION ALL
SELECT 
    'MDM_DATA_OUTAGE_STATISTICS' as TABLE_NAME,
    COUNT(*) as TOTAL_RECORDS
FROM MDM_DATA_OUTAGE_STATISTICS 
WHERE ORG_ID = '89344c8b02004e5692baa58a7bfabab7';

-- ============================================================================
-- 完成
-- ============================================================================

SELECT '============================================================================' as message;
SELECT 'MDM V7.0 Dashboard完整测试数据生成完成！' as message;
SELECT '============================================================================' as message;
SELECT '已整合的功能:' as message;
SELECT '1. Asset Dashboard - 设备配置统计和数据收集进度统计' as message;
SELECT '2. Data Analysis Dashboard - 设备事件、VEE事件、VEE数据统计' as message;
SELECT '3. Energy Analysis Dashboard - 损耗对象、计费、汇总对象统计' as message;
SELECT '4. Summary Object Statistics - 完整的表结构和数据' as message;
SELECT '5. 停电统计数据' as message;
SELECT '' as message;
SELECT '修复的问题:' as message;
SELECT '1. VEE Estimation Statistics - 解决exceptionId为undefined的问题' as message;
SELECT '2. Summary Object Statistics - 创建完整的表结构和数据' as message;
SELECT '3. Billing Statistics - 修复表格下半部分没有数据的问题' as message;
SELECT '' as message;
SELECT '目标ORG_ID: 89344c8b02004e5692baa58a7bfabab7' as message;
SELECT '数据确保不减少，所有原有功能得到保留和增强' as message;
SELECT '============================================================================' as message;